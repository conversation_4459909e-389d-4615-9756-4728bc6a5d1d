<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Domain.Shared\TaskTracking.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
    <PackageReference Include="Volo.Abp.FluentValidation" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.ObjectExtending" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.1.3" />
  </ItemGroup>

</Project>
