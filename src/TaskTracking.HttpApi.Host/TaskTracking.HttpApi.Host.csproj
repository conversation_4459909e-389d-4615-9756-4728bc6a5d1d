<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>TaskTracking</RootNamespace>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
    <UserSecretsId>TaskTracking-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.5" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.MultiTenancy" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.1.0-preview*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskTracking.Application\TaskTracking.Application.csproj" />
    <ProjectReference Include="..\TaskTracking.EntityFrameworkCore\TaskTracking.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\TaskTracking.HttpApi\TaskTracking.HttpApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

</Project>
