@using TaskTracking.TaskGroupAggregate.Dtos.TaskItems
@using TaskTracking.TaskGroupAggregate.TaskItems
@using TaskTracking.TaskGroupAggregate
@inherits TaskTrackingComponentBase
@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@inject ITaskGroupAppService TaskGroupAppService
<MudCard Class="task-item-card h-100" Elevation="2" Style="display: flex; padding: 1.5rem !important; flex-wrap: nowrap; justify-content: space-between; flex-direction: column;">    <MudCardHeader>
        <CardHeaderContent>
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <MudText Typo="Typo.h6" Class="task-item-title">
                        @TaskItem.Title
                    </MudText>
                    <MudText Typo="Typo.caption" Class="text-muted">
                        <MudIcon Icon="@GetTaskTypeIcon()" Size="Size.Small" Class="me-1" />
                        @L[TaskItem.TaskType.ToString()]
                    </MudText>
                </div>
                <div class="task-item-status">
                    @if (TaskItem.IsCompleted)
                    {
                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                            @L["Completed"]
                        </MudChip>
                    }
                    else if (IsOverdue())
                    {
                        <MudChip T="string" Color="Color.Error" Size="Size.Small" Icon="@Icons.Material.Filled.Warning">
                            @L["Overdue"]
                        </MudChip>
                    }
                    else if (IsDueToday())
                    {
                        <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Today">
                            @L["DueToday"]
                        </MudChip>
                    }
                    else
                    {
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                            @L["Pending"]
                        </MudChip>
                    }
                </div>
            </div>
        </CardHeaderContent>
    </MudCardHeader>
    
    <MudCardContent Class="pb-2">
        <MudText Typo="Typo.body2" Class="task-item-description mb-3">
            @(TaskItem.Description.Length > 100 ? TaskItem.Description.Substring(0, 100) + "..." : TaskItem.Description)
        </MudText>
        
        <!-- Date Information -->
        <div class="mb-3">
            <MudText Typo="Typo.caption" Class="text-muted">
                <MudIcon Icon="@Icons.Material.Filled.CalendarToday" Size="Size.Small" Class="me-1" />
                @L["StartDate"]: @TaskItem.StartDate.ToString("dd/MM/yyyy")
            </MudText>
            @if (TaskItem.EndDate.HasValue)
            {
                <MudText Typo="Typo.caption" Class="text-muted">
                    <MudIcon Icon="@Icons.Material.Filled.Event" Size="Size.Small" Class="me-1" />
                    @L["EndDate"]: @TaskItem.EndDate.Value.ToString("dd/MM/yyyy")
                </MudText>
            }
        </div>
        
        <!-- Recurrence Information -->
        @if (TaskItem.TaskType == TaskType.Recurring && TaskItem.RecurrencePattern != null)
        {
            <div class="mb-3">
                <MudText Typo="Typo.caption" Class="text-muted">
                    <MudIcon Icon="@Icons.Material.Filled.Repeat" Size="Size.Small" Class="me-1" />
                    @L["Recurrence"]: @GetRecurrenceText()
                </MudText>
            </div>
        }
        
        <!-- Progress Information -->
        @if (TaskItem.UserTaskProgressDtos.Any())
        {
            <div class="mt-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <MudText Typo="Typo.caption" Class="text-muted">
                        <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Small" Class="me-1" />
                        @L["Progress"]
                    </MudText>
                    <MudText Typo="Typo.caption" Class="progress-text">
                        @GetProgressPercentage()%
                    </MudText>
                </div>
                <MudProgressLinear Color="@GetProgressColor()" 
                                   Value="@GetProgressPercentage()"
                                   Class="progress-bar" />
            </div>
        }
    </MudCardContent>
    
    <MudCardActions Class="d-flex justify-content-between">
        <div class="d-flex gap-2">
            <MudButton Variant="Variant.Text"
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Visibility"
                       Size="Size.Small"
                       OnClick="@(() => ViewTask())">
                @L["View"]
            </MudButton>

            @if (!TaskItem.IsCompleted)
            {
                <MudButton Variant="Variant.Text"
                           Color="Color.Success"
                           StartIcon="@Icons.Material.Filled.CheckCircle"
                           Size="Size.Small"
                           OnClick="@(() => RecordProgress())">
                    @L["RecordProgress"]
                </MudButton>
            }
        </div>

        <div>
            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                           Color="Color.Primary"
                           Size="Size.Small"
                           OnClick="@(() => EditTask())"
                           title="@L["Edit"]" />
            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                           Color="Color.Error"
                           Size="Size.Small"
                           OnClick="@(() => DeleteTask())"
                           title="@L["Delete"]" />
        </div>
    </MudCardActions>
</MudCard>

@code {
    [Parameter] public TaskItemDto TaskItem { get; set; } = null!;
    [Parameter] public Guid TaskGroupId { get; set; }
    [Parameter] public EventCallback OnTaskDeleted { get; set; }
    [Parameter] public EventCallback OnTaskUpdated { get; set; }

    private string GetTaskTypeIcon()
    {
        return TaskItem.TaskType switch
        {
            TaskType.OneTime => Icons.Material.Filled.Event,
            TaskType.Recurring => Icons.Material.Filled.Repeat,
            _ => Icons.Material.Filled.Assignment
        };
    }

    private bool IsOverdue()
    {
        return !TaskItem.IsCompleted && 
               TaskItem.EndDate.HasValue && 
               TaskItem.EndDate.Value.Date < Clock.Now.Date;
    }

    private bool IsDueToday()
    {
        return TaskItem.IsDueToday;
    }

    private string GetRecurrenceText()
    {
        if (TaskItem.RecurrencePattern == null) return "";
        
        return TaskItem.RecurrencePattern.RecurrenceType switch
        {
            RecurrenceType.Daily => L["Daily"],
            RecurrenceType.Weekly => L["Weekly"],
            RecurrenceType.Monthly => L["Monthly"],
            _ => L["Custom"]
        };
    }

    private int GetProgressPercentage()
    {
        if (!TaskItem.UserTaskProgressDtos.Any()) return 0;
        
        var completedDays = TaskItem.UserTaskProgressDtos.Count(p => p.ProgressPercentage == 100);
        var totalDays = TaskItem.UserTaskProgressDtos.Count;
        
        return totalDays > 0 ? (int)Math.Round((double)completedDays / totalDays * 100) : 0;
    }

    private Color GetProgressColor()
    {
        var percentage = GetProgressPercentage();
        return percentage switch
        {
            >= 80 => Color.Success,
            >= 50 => Color.Warning,
            _ => Color.Primary
        };
    }

    private void ViewTask()
    {
        NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}/tasks/{TaskItem.Id}");
    }

    private void EditTask()
    {
        NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}/tasks/{TaskItem.Id}/edit");
    }

    private async Task RecordProgress()
    {
        var parameters = new DialogParameters
        {
            ["TaskGroupId"] = TaskGroupId,
            ["TaskProgressDetail"] = await GetTaskProgressDetail()
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseButton = true,
        };

        var result = await DialogService.ShowAsync<ProgressRecordingDialog>(L["RecordProgress"], parameters, options);
        var dialogResult = await result.Result;

        if (dialogResult is { Canceled: false, Data: true })
        {
            await OnTaskUpdated.InvokeAsync();
        }

    }

    private async Task<TaskProgressDetailDto?> GetTaskProgressDetail()
    {
        try
        {
            return await TaskGroupAppService.GetTaskProgressDetailAsync(TaskGroupId, TaskItem.Id);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting task progress detail: {ex.Message}");
            return null;
        }
    }

    private async Task DeleteTask()
    {
        var parameters = new DialogParameters<ConfirmationDialog>
        {
            { x => x.ContentText, string.Format(L["DeleteTaskConfirmation"], TaskItem.Title) },
            { x => x.ButtonText, L["Delete"] },
            { x => x.Color, Color.Error }
        };

        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };

        var result = await DialogService.ShowAsync<ConfirmationDialog>(L["DeleteTask"], parameters, options);
        var dialogResult = await result.Result;

        if (!dialogResult.Canceled && dialogResult.Data is true)
        {
            try
            {
                await TaskGroupAppService.DeleteTaskItemAsync(TaskGroupId, TaskItem.Id);
                await OnTaskDeleted.InvokeAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting task: {ex.Message}");
                // You might want to show an error message to the user here
            }
        }
    }
}