<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.6.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.6.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MudBlazor" Version="8.6.0" />
    <PackageReference Include="MudBlazor.Markdown" Version="8.6.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme" Version="4.1.0-preview*" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Autofac.WebAssembly" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.Blazor.WebAssembly" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.TenantManagement.Blazor.WebAssembly" Version="9.1.3" />
    <PackageReference Include="Volo.Abp.SettingManagement.Blazor.WebAssembly" Version="9.1.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\TaskTracking.HttpApi.Client\TaskTracking.HttpApi.Client.csproj" />
  </ItemGroup>


</Project>
